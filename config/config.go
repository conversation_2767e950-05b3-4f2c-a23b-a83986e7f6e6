package config

import (
	"fmt"
	"github.com/spf13/viper"
	"os"
)

type AeronConfig struct {
	Aeron struct {
		Directory  string                `mapstructure:"directory"`
		Clients    map[string]Client     `mapstructure:"clients"`
		Cluster    Cluster               `mapstructure:"cluster"`
		Archive    Archive               `mapstructure:"archive"`
		Service    Service               `mapstructure:"service"`
		Subscriber map[string]Subscriber `mapstructure:"subscriber,omitempty"`
		Metrics    Metrics               `mapstructure:"metrics"`
	} `mapstructure:"aeron"`
}

type Client struct {
	TargetName       string `mapstructure:"target-name"`
	ClientID         uint64 `mapstructure:"client-id"`
	EgressChannel    string `mapstructure:"egress-channel,omitempty"`
	IngressChannel   string `mapstructure:"ingress-channel"`
	IngressEndpoints string `mapstructure:"ingress-endpoints"`
	IngressStreamID  int32  `mapstructure:"ingress-stream-id"`
	EgressStreamID   int32  `mapstructure:"egress-stream-id"`
	PoolSize         int    `mapstructure:"pool-size"` // Deprecated: no need
	Mode             string `mapstructure:"mode"`
	IdleStrategy     string `mapstructure:"idle-strategy"`
	Channel          string `mapstructure:"channel"`
	StreamID         int    `mapstructure:"stream-id"`
	RequestChannel   string `mapstructure:"request-channel"`
	RequestStreamID  int    `mapstructure:"request-stream-id"`
	ResponseChannel  string `mapstructure:"response-channel"`
	ResponseStreamID int32  `mapstructure:"response-stream-id"`
	BufferSize       int    `mapstructure:"buffer-size"`
}

type Cluster struct {
	Directory string `mapstructure:"directory"`
}

type Archive struct {
	ControlRequestChannel  string `mapstructure:"control-request-channel"`
	ControlResponseChannel string `mapstructure:"control-response-channel"`
	Directory              string `mapstructure:"directory"`
}

type Service struct {
	IdleStrategy string `mapstructure:"idle-strategy"`
}

type Subscriber struct {
	IdleStrategy     string `mapstructure:"idle-strategy"`
	Enabled          bool   `mapstructure:"enabled"`
	Mode             string `mapstructure:"mode"`
	Channel          string `mapstructure:"channel"`
	StreamID         int    `mapstructure:"stream-id"`
	ReplayChannel    string `mapstructure:"replay-channel"`
	RequestChannel   string `mapstructure:"request-channel"`
	RequestStreamID  int    `mapstructure:"request-stream-id"`
	ResponseChannel  string `mapstructure:"response-channel"`
	ResponseStreamID int    `mapstructure:"response-stream-id"`
}

type Metrics struct {
	Enabled bool `mapstructure:"enabled"`
	Port    int  `mapstructure:"port"`
}

func LoadConfig() (*AeronConfig, error) {
	var config AeronConfig
	cfg := viper.AllSettings()
	expandedCfg := expandEnvVars(cfg).(map[string]interface{})
	err := viper.MergeConfigMap(expandedCfg)
	if err != nil {
		return nil, err
	}
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}
	applyDefaults(&config)
	return &config, nil
}

func expandEnvVars(value interface{}) interface{} {
	switch v := value.(type) {
	case string:
		return os.ExpandEnv(v) // 替换 ${VAR} 或 $VAR
	case map[string]interface{}:
		for key, val := range v {
			v[key] = expandEnvVars(val) // 递归处理map
		}
		return v
	case []interface{}:
		for i, elem := range v {
			v[i] = expandEnvVars(elem) // 递归处理数组
		}
		return v
	default:
		return value // 非字符串类型直接返回
	}
}

func applyDefaults(config *AeronConfig) {
	if config.Aeron.Archive.ControlRequestChannel == "" {
		config.Aeron.Archive.ControlRequestChannel = "aeron:ipc?alias=cluster-service-archive-ctrl-req|term-length=128k"
	}
	if config.Aeron.Archive.ControlResponseChannel == "" {
		config.Aeron.Archive.ControlResponseChannel = "aeron:ipc?alias=cluster-service-archive-ctrl-resp|term-length=128k"
	}

	for name, client := range config.Aeron.Clients {
		if client.IngressChannel == "" {
			client.IngressChannel = "aeron:udp"
		}
		if client.IngressEndpoints == "" {
			client.IngressEndpoints = "0=localhost:0"
		}
		if client.IngressStreamID == 0 {
			client.IngressStreamID = 101
		}
		if client.PoolSize == 0 {
			client.PoolSize = 1
		}
		if client.Mode == "" {
			client.Mode = "cluster"
		}
		if client.IdleStrategy == "" {
			client.IdleStrategy = "busy_spin"
		}
		if client.Channel == "" {
			client.Channel = "aeron:udp?endpoint=localhost:0"
		}
		if client.StreamID == 0 {
			client.StreamID = 30
		}
		if client.RequestChannel == "" {
			client.RequestChannel = "aeron:udp?endpoint=localhost:8010"
		}
		if client.RequestStreamID == 0 {
			client.RequestStreamID = 10
		}
		if client.ResponseStreamID == 0 {
			client.ResponseStreamID = 21
		}
		if client.ResponseChannel == "" {
			client.ResponseChannel = "aeron:udp?endpoint=localhost:0"
		}
		if client.BufferSize == 0 {
			client.BufferSize = 262144
		}
		config.Aeron.Clients[name] = client
	}

	if config.Aeron.Service.IdleStrategy == "" {
		config.Aeron.Service.IdleStrategy = "backoff"
	}

	for name, sub := range config.Aeron.Subscriber {
		if sub.IdleStrategy == "" {
			sub.IdleStrategy = "backoff"
		}
		// Note: Enabled field should be explicitly set in config, no default override
		if sub.Mode == "" {
			sub.Mode = "merge"
		}
		if sub.Channel == "" {
			sub.Channel = "aeron:udp?endpoint=localhost:45888"
		}
		if sub.StreamID == 0 {
			sub.StreamID = 42
		}
		if sub.ReplayChannel == "" {
			sub.ReplayChannel = "aeron:udp?endpoint=localhost:38010"
		}
		if sub.RequestChannel == "" {
			sub.RequestChannel = "aeron:udp?endpoint=localhost:28010"
		}
		if sub.RequestStreamID == 0 {
			sub.RequestStreamID = 10
		}
		if sub.ResponseChannel == "" {
			sub.ResponseChannel = "aeron:udp?endpoint=localhost:0"
		}
		if sub.ResponseStreamID == 0 {
			sub.ResponseStreamID = 20
		}
		config.Aeron.Subscriber[name] = sub
	}

	// Set default metrics configuration
	if config.Aeron.Metrics.Port == 0 {
		config.Aeron.Metrics.Port = 8080
	}
}
