package aeronclient

import (
	"context"
	"errors"
	"fmt"
	"github.com/HydraXTrader/exchange-generic-aeron-sdk-go/common"
	"github.com/HydraXTrader/exchange-generic-aeron-sdk-go/config"
	"github.com/HydraXTrader/exchange-generic-aeron-sdk-go/log"
	"github.com/HydraXTrader/exchange-generic-aeron-sdk-go/message"
	aeronatomic "github.com/lirm/aeron-go/aeron/atomic"
	"github.com/lirm/aeron-go/cluster"
	"math/rand"
	"strings"
	"sync"
	"time"
)

var (
	ErrInvalidTopic      = errors.New("invalid message topic")
	ErrClientNotExist    = errors.New("client not exists")
	ErrClientNotReady    = errors.New("client not ready")
	ErrRoleNotLeader     = errors.New("current role is not leader")
	ErrClusterNotStarted = errors.New("cluster not started")
)

type ClientManager struct {
	mu               sync.RWMutex
	router           *message.Router
	config           *config.AeronConfig
	Cluster          cluster.Cluster
	clients          map[string][]AeronClient
	role             cluster.Role
	sequenceProvider common.ClientSequenceProvider
}

func NewClientManager(
	config *config.AeronConfig,
	router *message.Router,
	sp common.ClientSequenceProvider,
) *ClientManager {
	return &ClientManager{
		router:           router,
		config:           config,
		sequenceProvider: sp,
	}
}

func (m *ClientManager) Initialize() error {
	if err := m.validateConfig(); err != nil {
		return err
	}
	m.clients = NewClientFactory(m.config, m.sequenceProvider).CreateClients()
	NewKeepAliveAgent(m.clients, 5*time.Second).Start()
	return nil
}

func (m *ClientManager) Send(msg *message.AeronMessage) error {
	if msg.Topic == "" {
		return ErrInvalidTopic
	}

	rules := m.router.Route(*msg)
	for _, rule := range rules {
		if err := m.processSend(rule, msg); err != nil {
			return err
		}
	}
	return nil
}

func (m *ClientManager) processSend(rule *message.RouteRule, msg *message.AeronMessage) error {
	m.mu.RLock()
	defer m.mu.RUnlock()

	for _, target := range rule.TargetNames {
		clients, exists := m.clients[target]
		if !exists {
			return fmt.Errorf("%w: %s", ErrClientNotExist, target)
		}
		client := clients[0]
		seq := m.sequenceProvider(target, m.config.Aeron.Clients[target].ClientID)
		if m.role != cluster.Leader {
			return ErrRoleNotLeader
		}

		if err := client.Send(msg, seq); err != nil {
			log.Error("target: %s message send failed, err: %v", target, err)
			return fmt.Errorf("%w: %s", ErrClientNotReady, target)
		}
	}
	return nil
}

func (m *ClientManager) ChangeRole(newRole cluster.Role) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.role == newRole {
		return
	}

	m.role = newRole
	if newRole == cluster.Leader {
		m.activateLeader()
	}
}

func (m *ClientManager) activateLeader() {
	var wg sync.WaitGroup
	retryOpts := []retry.Option{
		retry.DelayType(func(n uint, err error, config *retry.Config) time.Duration {
			return retry.BackOffDelay(n, err, config)
		}),
		retry.MaxJitter(100 * time.Millisecond),
		retry.OnRetry(func(n uint, err error) {
			log.Warn("client connection retry attempt %d failed: %v", n, err)
		}),
	}
	for name, clients := range m.clients {
		for i, client := range clients {
			if archiveClient, ok := client.(*ArchiveClient); ok {
				wg.Add(1)
				go func(c *ArchiveClient, clientName string, idx int) {
					defer wg.Done()
					ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
					defer cancel()
					err := retry.Do(
						func() error {
							return c.Start()
						},
						append(retryOpts,
							retry.Attempts(10),
							retry.Context(ctx),
							retry.LastErrorOnly(true),
							retry.RetryIf(isRecoverableError),
						)...,
					)
					if err != nil {
						log.Error("client: %s connection failed", clientName)
					}
				}(archiveClient, name, i)
			}
		}
	}
	wg.Wait()
}

func isRecoverableError(err error) bool {
	return !strings.Contains(err.Error(), "fatal")
}

func (m *ClientManager) SendToCluster(msg *message.AeronMessage) error {
	if m.Cluster == nil {
		return ErrClusterNotStarted
	}
	buf := common.AcquireBuffer()
	defer common.ReleaseBuffer(buf)
	length, err := msg.MarshalToBuffer(buf)
	if err != nil {
		return err
	}
	buffer := aeronatomic.MakeBuffer(buf[len(buf)-length:])

	offer := m.Cluster.Offer(buffer, 0, int32(length))
	if offer < 0 {
		return fmt.Errorf("cluster offer failed: %d", offer)
	}
	return nil
}

func (m *ClientManager) validateConfig() error {
	targets := make(map[string]struct{})
	for _, rule := range m.router.Rules {
		for _, t := range rule.TargetNames {
			targets[t] = struct{}{}
		}
	}

	for name := range m.config.Aeron.Clients {
		if _, exists := targets[name]; !exists {
			log.Warn("unused client: %s config", name)
		}
	}

	for t := range targets {
		if _, exists := m.config.Aeron.Clients[t]; !exists {
			return fmt.Errorf("missing config for target: %s", t)
		}
	}
	return nil
}
