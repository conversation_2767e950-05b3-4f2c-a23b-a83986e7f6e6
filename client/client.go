package aeronclient

import (
	"errors"
	"fmt"
	"github.com/HydraXTrader/exchange-generic-aeron-sdk-go/log"
	"github.com/HydraXTrader/exchange-generic-aeron-sdk-go/message"
	"github.com/lirm/aeron-go/aeron"
	"time"
	"unsafe"
)

type AeronClient interface {
	Send(message *message.AeronMessage, seq uint64) error
	Start() error
	IsConnected() bool
	SendKeepAlive() bool
}

type BaseClient struct {
	clientName string
}

func (c *BaseClient) wrapFinalError(err error) error {
	var aeronErr *AeronError
	if errors.As(err, &aeronErr) {
		return fmt.Errorf("send failed after retries (code=%d): %w", aeronErr.Code, err)
	}
	return fmt.Errorf("send failed: %w", err)
}

func (c *BaseClient) logRetryAttempt(lastErrCode *int64, retryCount *uint) func(uint, error) {
	return func(n uint, _ error) {
		if *lastErrCode == aeron.NotConnected || *lastErrCode == aeron.PublicationClosed {
			*retryCount++
		}
		log.Trace("[%s] Retry #%d times for code %d", c.clientName, n+1, *lastErrCode)
	}
}

func (c *BaseClient) shouldRetry(lastErrCode *int64, retryCount *uint) func(error) bool {
	return func(err error) bool {
		var aeronErr *AeronError
		if !errors.As(err, &aeronErr) {
			return false
		}
		*lastErrCode = aeronErr.Code
		switch aeronErr.Code {
		case aeron.BackPressured, aeron.AdminAction:
			return true
		case aeron.NotConnected, aeron.PublicationClosed:
			return *retryCount < 5
		default:
			return false
		}
	}
}

func (c *BaseClient) retryOptions(lastErrCode *int64, retryCount *uint) []retry.Option {
	return []retry.Option{
		retry.Attempts(0),
		retry.Delay(500 * time.Millisecond),
		retry.MaxDelay(5 * time.Second),
		retry.DelayType(retry.BackOffDelay),
		retry.OnRetry(c.logRetryAttempt(lastErrCode, retryCount)),
		retry.RetryIf(c.shouldRetry(lastErrCode, retryCount)),
	}
}

func PutUint64(sequence uint64) []byte {
	seq := (*[8]byte)(unsafe.Pointer(&sequence))[:]
	return seq
}

func PutUint64toBytes(sequence uint64, bytes []byte) {
	seq := (*[8]byte)(unsafe.Pointer(&sequence))[:]
	copy(bytes, seq)
}
