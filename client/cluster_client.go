package aeronclient

import (
    "errors"
    "fmt"
    "github.com/HydraXTrader/exchange-generic-aeron-sdk-go/common"
    "github.com/HydraXTrader/exchange-generic-aeron-sdk-go/config"
    "github.com/HydraXTrader/exchange-generic-aeron-sdk-go/log"
    "github.com/HydraXTrader/exchange-generic-aeron-sdk-go/message"
    "github.com/lirm/aeron-go/aeron"
    "github.com/lirm/aeron-go/aeron/atomic"
    "github.com/lirm/aeron-go/cluster/client"
    "math/rand"
    "sync"
    "time"
    "unsafe"
)

var (
    ErrConnectionUnavailable = errors.New("connection unavailable")
)

type ClusterClient struct {
    BaseClient
    mu             sync.RWMutex
    directory      string
    config         *config.Client
    aeron          *aeron.Aeron
    cluster        *client.AeronCluster
    sequence       common.ClientSequenceProvider
    reconnectMu    sync.Mutex // 新增：重连状态锁
    isReconnecting bool
}

func (c *ClusterClient) SendKeepAlive() bool {
    if c.cluster == nil {
        return true
    }
    return c.cluster.SendKeepAlive()
}

func NewClusterClient(
    directory string,
    config *config.Client,
    seqProvider common.ClientSequenceProvider,
) *ClusterClient {
    return &ClusterClient{
        BaseClient:     BaseClient{clientName: config.TargetName},
        directory:      directory,
        config:         config,
        sequence:       seqProvider,
        isReconnecting: false,
    }
}

func (c *ClusterClient) Start() error {
    c.mu.Lock()
    defer c.mu.Unlock()

    if c.IsConnected() {
        return nil
    }

    ctxAeron := aeron.NewContext().AeronDir(c.directory)

    a, err := aeron.Connect(ctxAeron)
    if err != nil {
        return fmt.Errorf("aeron connect failed: %w", err)
    }
    opts := client.NewOptions()
    opts.IngressChannel = c.config.IngressChannel
    opts.EgressChannel = c.config.EgressChannel
    opts.IngressStreamId = c.config.IngressStreamID
    opts.IngressEndpoints = c.config.IngressEndpoints

    clusterClient, err := client.NewAeronCluster(ctxAeron, opts, NewDefaultEgressListener(c.clientName))
    if err != nil {
        err := a.Close()
        if err != nil {
            return err
        }
        return fmt.Errorf("cluster clusterClient create failed: %w", err)
    }

    c.aeron = a
    c.cluster = clusterClient
    currentTime := time.Now()
    for !clusterClient.IsConnected() {
        opts.IdleStrategy.Idle(clusterClient.Poll())
        if time.Since(currentTime).Milliseconds() > 300 {
            currentTime = time.Now()
            log.Warn("Awaiting Cluster Client To Connect!")
        }
    }
    log.Debug("clusterClient: %s, egress: %s, ingress: %s, connected", c.config.TargetName, c.config.EgressChannel, c.config.IngressEndpoints)

    go func() {
        for {
            if clusterClient.IsClosed() {
                log.Error("client: %s disconnected", c.config.TargetName)
                continue
            }
            opts.IdleStrategy.Idle(clusterClient.Poll())
        }
    }()
    return nil
}

func (c *ClusterClient) Send(msg *message.AeronMessage, seq uint64) error {
    if !c.checkConnection() {
        c.reconnectMu.Lock()
        if c.isReconnecting {
            c.reconnectMu.Unlock()
            return fmt.Errorf("connection unavailable: reconnection in progress")
        }
        connectError := c.Start()
        if connectError != nil {
            log.Error("client: %s connection failed", c.clientName)

            go func(c *ClusterClient, clientName string) {
                defer func() {
                    c.reconnectMu.Lock()
                    c.isReconnecting = false
                    c.reconnectMu.Unlock()
                }()
                c.isReconnecting = true

                // Hand-written retry mechanism for connection
                const maxRetries = 10
                baseDelay := 500 * time.Millisecond
                maxDelay := 3 * time.Second
                maxJitter := 100 * time.Millisecond

                for attempt := 1; attempt <= maxRetries; attempt++ {
                    err := c.Start()
                    if err == nil {
                        log.Debug("client: %s connection successful on attempt %d", clientName, attempt)
                        return
                    }

                    // Check if error is recoverable
                    if !isRecoverableError(err) {
                        log.Error("client: %s connection failed with non-recoverable error: %v", clientName, err)
                        return
                    }

                    log.Warn("client connection retry attempt %d failed: %v", attempt, err)

                    if attempt < maxRetries {
                        // Calculate backoff delay with jitter
                        delay := baseDelay * time.Duration(1<<uint(attempt-1))
                        if delay > maxDelay {
                            delay = maxDelay
                        }
                        jitter := time.Duration(rand.Int63n(int64(maxJitter)))
                        delay += jitter

                        time.Sleep(delay)
                    }
                }
                log.Error("client: %s connection failed after %d attempts", clientName, maxRetries)
            }(c, c.clientName)
            return connectError
        }
        c.reconnectMu.Unlock()
    }
    buffer := common.AcquireBuffer()
    defer common.ReleaseBuffer(buffer)
    length, err := msg.MarshalToBuffer(buffer)
    buffer = buffer[:length]
    log.Debug("MarshalToBuffer client: %s, buffer length: %d, size: %d", c.clientName, len(buffer), length)
    if err != nil {
        return err
    }
    buf := c.buildMessageBuffer(buffer, length, seq)

    // Hand-written retry mechanism for send operation
    var (
        lastErrCode int64
        retryCount  uint
    )

    baseDelay := 500 * time.Millisecond
    maxDelay := 5 * time.Second

    for {
        result := c.cluster.Offer(buf, 0, int32(16+length))
        if result >= 0 {
            log.Trace("clusterClient: %s, egress: %s, ingress: %s, offer result: %d", c.config.TargetName, c.config.EgressChannel, c.config.IngressEndpoints, result)
            return nil
        }

        lastErrCode = result
        aeronErr := &AeronError{Code: result}

        // Check if we should retry
        shouldRetry := false
        switch result {
        case aeron.BackPressured, aeron.AdminAction:
            shouldRetry = true
        case aeron.NotConnected, aeron.PublicationClosed:
            shouldRetry = retryCount < 5
            if shouldRetry {
                retryCount++
            }
        }

        if !shouldRetry {
            return c.wrapFinalError(aeronErr)
        }

        log.Trace("[%s] Retry #%d times for code %d", c.clientName, retryCount, lastErrCode)

        // Calculate backoff delay
        delay := baseDelay * time.Duration(1<<uint(retryCount))
        if delay > maxDelay {
            delay = maxDelay
        }

        time.Sleep(delay)
    }
}

// sendOperation method removed - logic is now inlined in Send method for better performance

func (c *ClusterClient) buildMessageBuffer(buffer []byte, length int, sequence uint64) *atomic.Buffer {
    clientID := c.config.ClientID
    seq := (*[8]byte)(unsafe.Pointer(&sequence))[:]
    clientId := (*[8]byte)(unsafe.Pointer(&clientID))[:]
    return atomic.MakeBuffer(append(append(clientId, seq...), buffer[len(buffer)-length:]...))
}

func (c *ClusterClient) checkConnection() bool {
    c.mu.RLock()
    defer c.mu.RUnlock()
    return c.cluster != nil && c.IsConnected()
}

func (c *ClusterClient) IsConnected() bool {
    return c.cluster != nil && c.cluster.IsConnected()
}

func (c *ClusterClient) Close() error {
    c.mu.Lock()
    defer c.mu.Unlock()

    if c.cluster != nil {
        c.cluster.Close()
    }
    if c.aeron != nil {
        err := c.aeron.Close()
        if err != nil {
            return err
        }
    }
    return nil
}
