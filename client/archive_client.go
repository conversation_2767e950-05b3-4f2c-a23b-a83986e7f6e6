package aeronclient

import (
	"fmt"
	"github.com/HydraXTrader/exchange-generic-aeron-sdk-go/common"
	"github.com/HydraXTrader/exchange-generic-aeron-sdk-go/config"
	"github.com/HydraXTrader/exchange-generic-aeron-sdk-go/log"
	"github.com/HydraXTrader/exchange-generic-aeron-sdk-go/message"
	"github.com/lirm/aeron-go/aeron"
	"github.com/lirm/aeron-go/aeron/atomic"
	aeronUtil "github.com/lirm/aeron-go/aeron/util"
	"github.com/lirm/aeron-go/archive"
	"github.com/lirm/aeron-go/archive/codecs"
	"strings"
	"sync"
	"time"
	"unsafe"
)

var ErrCannotExtendActiveRecording = "cannot extend active recording"
var ErrRecordingExists = "recording exists"

type ArchiveClient struct {
	BaseClient
	config      *config.Client
	directory   string
	aeronClient *aeron.Aeron
	archive     *archive.Archive
	publication *aeron.Publication
	recordingID int64
	Sequence    common.ClientSequenceProvider
	mu          sync.Mutex
}

func (c *ArchiveClient) SendKeepAlive() bool {
	log.Trace("SendKeepAlive is not supported for ArchiveClient")
	return true
}

func (c *ArchiveClient) IsConnected() bool {
	return c.publication != nil && c.publication.IsConnected()
}

type ArchiveConfig struct {
	Channel          string
	StreamID         int32
	RecordingEnabled bool
}

type AeronError struct {
	Code int64
}

func (e *AeronError) Error() string {
	return fmt.Sprintf("aeron error: code=%d", e.Code)
}

func NewArchiveClient(directory string, config *config.Client, sp common.ClientSequenceProvider) *ArchiveClient {
	return &ArchiveClient{
		BaseClient: BaseClient{clientName: config.TargetName},
		directory:  directory,
		config:     config,
		Sequence:   sp,
	}
}

func (c *ArchiveClient) Start() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	ctx := aeron.NewContext().AeronDir(c.directory)

	options := archive.DefaultOptions()
	options.RequestChannel = c.config.RequestChannel
	options.ResponseChannel = c.config.ResponseChannel

	arch, err := archive.NewArchive(options, ctx)
	if err != nil {
		log.Error("Failed to connect to Aeron")
		return err
	}
	pubChannel, err := ExtendOrStartRecording(arch, c.config.Channel, int32(c.config.StreamID))
	if err != nil {
		log.Error("Failed to extend or start recording")
		return err
	}

	c.publication, err = arch.AddExclusivePublication(pubChannel, int32(c.config.StreamID))
	if err != nil {
		log.Error("Failed to add publication")
		return err
	}
	ctMaxWait := 0
	maxWaxWaitPubOutToConnect := 1000 * 60
	for !c.publication.IsConnected() {
		log.Trace("Waiting for publication: %s to connect...", c.config.TargetName)
		time.Sleep(1000 * time.Millisecond)
		ctMaxWait += 1000
		if ctMaxWait > maxWaxWaitPubOutToConnect {
			log.Error("GIVE UP Waiting Publication To Connect!")
			return fmt.Errorf("connect to %s timeout, spent: %dms", c.config.TargetName, maxWaxWaitPubOutToConnect)
		}
	}
	return nil
}

func ExtendOrStartRecording(arch *archive.Archive, channel string, streamId int32) (string, error) {
	descs, err := arch.ListRecordingsForUri(0, 1000, channel, streamId)
	if err != nil {
		log.Error("Failed to list recordings")
		return "", err
	}
	pubChannel := channel
	if len(descs) == 0 {
		_, err := arch.StartRecording(channel, streamId, false, true)
		if err != nil {
			log.Error("Failed to start recording")
			return "", err
		}
		log.Debug("Started new recording")
		return pubChannel, nil
	}
	descriptor := descs[len(descs)-1]
	var recordingId = descriptor.RecordingId

	if recordingId < 0 {
		_, err := arch.StartRecording(channel, streamId, false, true)
		if err != nil {
			log.Error("Failed to start recording")
			return "", err
		}
		log.Debug("Started new recording")
	} else {
		_, err := arch.ExtendRecording(recordingId, streamId,
			codecs.SourceLocation.REMOTE, true, channel)
		if err != nil {
			log.Error("Failed to extend recording")
			if strings.Contains(err.Error(), ErrCannotExtendActiveRecording) {
				log.Debug("stopping active recording")
				err = arch.StopRecording(channel, streamId)
				if err != nil {
					log.Error("unable to stop active recording %s\n", err.Error())
					return "", err
				}
				time.Sleep(time.Second)
				log.Debug("re-extend recording")
				if _, err = arch.ExtendRecording(recordingId, streamId, codecs.SourceLocation.LOCAL, true, channel); err != nil {
					log.Error("failed to re-extend recording >> err=%s\n", err.Error())
					return "", err
				}
				return pubChannel, nil
			} else if strings.Contains(err.Error(), ErrRecordingExists) {
				return pubChannel, nil
			}
			return "", err
		}
	}
	termID := ComputeTermIDFromPosition(descriptor.StopPosition, descriptor.TermBufferLength, descriptor.InitialTermId)
	termOffset := int32(descriptor.StopPosition) & (descriptor.TermBufferLength - 1)
	pubChannel = fmt.Sprintf("%s|term-length=%d|init-term-id=%d|term-offset=%d|term-id=%d",
		channel, descriptor.TermBufferLength, descriptor.InitialTermId, termOffset, termID)
	log.Debug("Extended existing recording %d", recordingId)
	return pubChannel, nil
}

func ComputeTermIDFromPosition(position int64, termBufferLength int32, initialTermID int32) int32 {
	bitsToShift := aeronUtil.NumberOfTrailingZeroes(uint32(termBufferLength))
	return int32(position>>bitsToShift) + initialTermID
}

func (c *ArchiveClient) Send(msg *message.AeronMessage, seq uint64) error {
	if !c.IsConnected() {
		err := c.Start()
		if err != nil {
			return err
		}
	}
	buffer := common.AcquireBuffer()
	defer common.ReleaseBuffer(buffer)
	length, err := msg.MarshalToBuffer(buffer)
	if err != nil {
		return err
	}
	buffer = buffer[:length]
	buf := c.buildMessageBuffer(buffer, length, seq)

	// Hand-written retry mechanism for send operation
	var (
		lastErrCode int64
		retryCount  uint
	)

	baseDelay := 500 * time.Millisecond
	maxDelay := 5 * time.Second

	for {
		result := c.publication.Offer(buf, 0, int32(16+length), nil)
		if result >= 0 {
			return nil
		}

		lastErrCode = result
		aeronErr := &AeronError{Code: result}

		// Check if we should retry
		shouldRetry := false
		switch result {
		case aeron.BackPressured, aeron.AdminAction:
			shouldRetry = true
		case aeron.NotConnected, aeron.PublicationClosed:
			shouldRetry = retryCount < 5
			if shouldRetry {
				retryCount++
			}
		}

		if !shouldRetry {
			return c.wrapFinalError(aeronErr)
		}

		log.Trace("[%s] Retry #%d times for code %d", c.clientName, retryCount, lastErrCode)

		// Calculate backoff delay
		delay := baseDelay * time.Duration(1 << uint(retryCount))
		if delay > maxDelay {
			delay = maxDelay
		}

		time.Sleep(delay)
	}
}

func (c *ArchiveClient) buildMessageBuffer(buffer []byte, length int, sequence uint64) *atomic.Buffer {
	clientID := c.config.ClientID
	seq := (*[8]byte)(unsafe.Pointer(&sequence))[:]
	clientId := (*[8]byte)(unsafe.Pointer(&clientID))[:]
	return atomic.MakeBuffer(append(append(clientId, seq...), buffer[len(buffer)-length:]...))
}

// sendOperation method removed - logic is now inlined in Send method for better performance
