package metrics

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/HydraXTrader/exchange-generic-aeron-sdk-go/log"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

// MetricsServer manages the HTTP server for Prometheus metrics
type MetricsServer struct {
	server *http.Server
	port   int
}

// NewMetricsServer creates a new metrics server
func NewMetricsServer(port int) *MetricsServer {
	mux := http.NewServeMux()
	mux.Handle("/metrics", promhttp.Handler())
	mux.HandleFunc("/health", healthHandler)

	server := &http.Server{
		Addr:         fmt.Sprintf(":%d", port),
		Handler:      mux,
		ReadTimeout:  10 * time.Second,
		WriteTimeout: 10 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	return &MetricsServer{
		server: server,
		port:   port,
	}
}

// Start starts the metrics server
func (ms *MetricsServer) Start() error {
	log.Info("Starting metrics server on port %d", ms.port)
	
	go func() {
		if err := ms.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Error("Metrics server failed to start: %v", err)
		}
	}()
	
	return nil
}

// Stop gracefully stops the metrics server
func (ms *MetricsServer) Stop(ctx context.Context) error {
	log.Info("Stopping metrics server")
	return ms.server.Shutdown(ctx)
}

// healthHandler provides a simple health check endpoint
func healthHandler(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusOK)
	w.Write([]byte("OK"))
}
