package metrics

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

var (
	// Back pressure counters for different client types
	BackPressureCounter = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "aeron_back_pressure_total",
			Help: "Total number of back pressure events encountered",
		},
		[]string{"client_name", "client_type", "error_code"},
	)

	// Admin action counters
	AdminActionCounter = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "aeron_admin_action_total",
			Help: "Total number of admin action events encountered",
		},
		[]string{"client_name", "client_type", "error_code"},
	)

	// Not connected counters
	NotConnectedCounter = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "aeron_not_connected_total",
			Help: "Total number of not connected events encountered",
		},
		[]string{"client_name", "client_type", "error_code"},
	)

	// Publication closed counters
	PublicationClosedCounter = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "aeron_publication_closed_total",
			Help: "Total number of publication closed events encountered",
		},
		[]string{"client_name", "client_type", "error_code"},
	)

	// Retry counters
	RetryCounter = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "aeron_retry_attempts_total",
			Help: "Total number of retry attempts",
		},
		[]string{"client_name", "client_type", "retry_reason"},
	)

	// Send success counters
	SendSuccessCounter = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "aeron_send_success_total",
			Help: "Total number of successful sends",
		},
		[]string{"client_name", "client_type"},
	)

	// Send failure counters
	SendFailureCounter = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "aeron_send_failure_total",
			Help: "Total number of failed sends",
		},
		[]string{"client_name", "client_type", "error_code"},
	)

	// Connection retry counters
	ConnectionRetryCounter = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "aeron_connection_retry_total",
			Help: "Total number of connection retry attempts",
		},
		[]string{"client_name", "client_type"},
	)

	// Connection success counters
	ConnectionSuccessCounter = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "aeron_connection_success_total",
			Help: "Total number of successful connections",
		},
		[]string{"client_name", "client_type"},
	)

	// Connection failure counters
	ConnectionFailureCounter = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "aeron_connection_failure_total",
			Help: "Total number of failed connections",
		},
		[]string{"client_name", "client_type"},
	)

	// Subscriber metrics
	SubscriberFragmentsPolled = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "aeron_subscriber_fragments_polled_total",
			Help: "Total number of fragments polled by subscribers",
		},
		[]string{"subscriber_name"},
	)

	SubscriberConnectionRetry = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "aeron_subscriber_connection_retry_total",
			Help: "Total number of subscriber connection retries",
		},
		[]string{"subscriber_name"},
	)
)

// Helper functions to record metrics with proper labels

// RecordBackPressure records a back pressure event
func RecordBackPressure(clientName, clientType string, errorCode int64) {
	BackPressureCounter.WithLabelValues(clientName, clientType, formatErrorCode(errorCode)).Inc()
}

// RecordAdminAction records an admin action event
func RecordAdminAction(clientName, clientType string, errorCode int64) {
	AdminActionCounter.WithLabelValues(clientName, clientType, formatErrorCode(errorCode)).Inc()
}

// RecordNotConnected records a not connected event
func RecordNotConnected(clientName, clientType string, errorCode int64) {
	NotConnectedCounter.WithLabelValues(clientName, clientType, formatErrorCode(errorCode)).Inc()
}

// RecordPublicationClosed records a publication closed event
func RecordPublicationClosed(clientName, clientType string, errorCode int64) {
	PublicationClosedCounter.WithLabelValues(clientName, clientType, formatErrorCode(errorCode)).Inc()
}

// RecordRetry records a retry attempt
func RecordRetry(clientName, clientType, reason string) {
	RetryCounter.WithLabelValues(clientName, clientType, reason).Inc()
}

// RecordSendSuccess records a successful send
func RecordSendSuccess(clientName, clientType string) {
	SendSuccessCounter.WithLabelValues(clientName, clientType).Inc()
}

// RecordSendFailure records a failed send
func RecordSendFailure(clientName, clientType string, errorCode int64) {
	SendFailureCounter.WithLabelValues(clientName, clientType, formatErrorCode(errorCode)).Inc()
}

// RecordConnectionRetry records a connection retry attempt
func RecordConnectionRetry(clientName, clientType string) {
	ConnectionRetryCounter.WithLabelValues(clientName, clientType).Inc()
}

// RecordConnectionSuccess records a successful connection
func RecordConnectionSuccess(clientName, clientType string) {
	ConnectionSuccessCounter.WithLabelValues(clientName, clientType).Inc()
}

// RecordConnectionFailure records a failed connection
func RecordConnectionFailure(clientName, clientType string) {
	ConnectionFailureCounter.WithLabelValues(clientName, clientType).Inc()
}

// RecordSubscriberFragmentsPolled records fragments polled by a subscriber
func RecordSubscriberFragmentsPolled(subscriberName string, count int) {
	SubscriberFragmentsPolled.WithLabelValues(subscriberName).Add(float64(count))
}

// RecordSubscriberConnectionRetry records a subscriber connection retry
func RecordSubscriberConnectionRetry(subscriberName string) {
	SubscriberConnectionRetry.WithLabelValues(subscriberName).Inc()
}

// Helper function to format error codes consistently
func formatErrorCode(code int64) string {
	switch code {
	case -1:
		return "back_pressured"
	case -2:
		return "not_connected"
	case -3:
		return "admin_action"
	case -4:
		return "publication_closed"
	case -5:
		return "max_position_exceeded"
	case -6:
		return "publication_not_connected"
	default:
		return "unknown"
	}
}
