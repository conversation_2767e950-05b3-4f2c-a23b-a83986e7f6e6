package archive

import (
	"context"
	"fmt"
	"github.com/HydraXTrader/exchange-generic-aeron-sdk-go/common"
	"github.com/HydraXTrader/exchange-generic-aeron-sdk-go/config"
	"github.com/HydraXTrader/exchange-generic-aeron-sdk-go/log"
	"github.com/HydraXTrader/exchange-generic-aeron-sdk-go/metrics"
	"github.com/lirm/aeron-go/aeron"
	"github.com/lirm/aeron-go/aeron/idlestrategy"
	"github.com/lirm/aeron-go/archive"
	"github.com/lirm/aeron-go/archive/codecs"
	"github.com/lirm/aeron-go/archive/replaymerge"
	"math"
	"sync"
	"time"
)

type SubscriberManager struct {
	mu            sync.RWMutex
	ctx           context.Context
	cancel        context.CancelFunc
	aeronConfig   *config.AeronConfig
	positionStore common.PositionProvider
	failedSubs    map[string]struct{}
	wg            sync.WaitGroup
}

func NewSubscriberManager(
	parent context.Context,
	aeronConfig *config.AeronConfig,
	position common.PositionProvider,
) *SubscriberManager {
	ctx, cancel := context.WithCancel(parent)
	return &SubscriberManager{
		ctx:           ctx,
		cancel:        cancel,
		aeronConfig:   aeronConfig,
		positionStore: position,
		failedSubs:    make(map[string]struct{}),
	}
}

func (m *SubscriberManager) Start() error {
	m.mu.Lock()
	defer m.mu.Unlock()
	if len(m.aeronConfig.Aeron.Subscriber) == 0 {
		log.Fatal("No subscribers configured")
	}
	for subName := range m.aeronConfig.Aeron.Subscriber {
		if m.aeronConfig.Aeron.Subscriber[subName].Enabled == false {
			continue
		}
		if err := m.createRunner(subName); err != nil {
			log.Error("subscriber: %s connect failed, error: %v", subName, err)
			m.failedSubs[subName] = struct{}{}
		}
	}
	go m.retryLoop()
	return nil
}

func (m *SubscriberManager) createRunner(subName string) error {
	log.Debug("subscriber: %s create runner", subName)
	cfg, exists := m.aeronConfig.Aeron.Subscriber[subName]
	if !exists {
		return fmt.Errorf("missing subscriber config for %s", subName)
	}
	aeronContext := m.newAeronContext()
	aeronConn, err := aeron.Connect(aeronContext)
	if err != nil {
		return fmt.Errorf("aeron connect failed: %w", err)
	}
	ops := archive.DefaultOptions()
	ops.RequestChannel = cfg.RequestChannel
	ops.RequestStream = int32(cfg.RequestStreamID)
	ops.ResponseStream = int32(cfg.ResponseStreamID)
	ops.ResponseChannel = cfg.ResponseChannel
	log.Debug("subscriber: %s connect to archive: %s", subName, ops)
	arch, err := archive.NewArchive(ops, aeronContext)
	if err != nil {
		return fmt.Errorf("archive: %v connect failed: %w", ops, err)
	}
	var sub *aeron.Subscription
	streamId := int32(cfg.StreamID)
	descs, err := arch.ListRecordingsForUri(
		0,
		1000,
		cfg.Channel,
		streamId,
	)
	if err != nil {
		log.Error("subscriber: %s list recordings failed", subName)
		err = arch.Close()
		if err != nil {
			return err
		}
		err = aeronConn.Close()
		if err != nil {
			return err
		}
		return fmt.Errorf("%s list recordings failed: %w", subName, err)
	}
	if len(descs) == 0 {
		log.Debug("no recording found")
		sub, err = arch.AddSubscription(cfg.Channel, streamId)
		if err != nil {
			return fmt.Errorf("%s create subscriber failed: %w", subName, err)
		}
	} else {
		log.Debug("subscriber: %s list recordings: %v", subName, descs[len(descs)-1])
		sub, err = m.createReplaySubscription(aeronConn, arch, descs[len(descs)-1], &cfg, subName)
		if err != nil {
			err1 := arch.Close()
			if err1 != nil {
				return err1
			}
			err2 := aeronConn.Close()
			if err2 != nil {
				return err2
			}
			return fmt.Errorf("%s replay merge failed: %w", subName, err)
		}
	}
	go func() {
		defer func() {
			m.mu.Lock()
			defer m.mu.Unlock()
			if m.ctx.Err() == nil && !sub.IsConnected() {
				if _, exist := m.failedSubs[subName]; !exist {
					m.failedSubs[subName] = struct{}{}
					log.Warn("subscriber: %s disconnected", subName)
				}
			}
		}()
		fragmentHandler, err := GetHandlerProvider().Get(subName)
		assembler := aeron.NewFragmentAssembler(fragmentHandler.OnFragment, 1024)
		if err != nil {
			log.Fatal("%s get handler failed", subName, err)
		}
		for {
			select {
			case <-m.ctx.Done():
				log.Warn("subscriber: %s context done", subName)
				return
			default:
				// Hand-written retry mechanism for better performance
				if !sub.IsConnected() {
					const maxRetries = 10
					const retryDelay = time.Second

					for attempt := 1; attempt <= maxRetries; attempt++ {
						select {
						case <-m.ctx.Done():
							log.Warn("subscriber: %s context done during retry", subName)
							return
						default:
							if sub.IsConnected() {
								break // Connection successful
							}

							log.Debug("Connection retry attempt %d/%d for subscriber: %s", attempt, maxRetries, subName)

							if attempt < maxRetries {
								// Use a timer for cancellable sleep
								timer := time.NewTimer(retryDelay)
								select {
								case <-m.ctx.Done():
									timer.Stop()
									log.Warn("subscriber: %s context done during retry delay", subName)
									return
								case <-timer.C:
									// Continue to next retry attempt
								}
							}
						}
					}

					if !sub.IsConnected() {
						log.Error("Failed to connect after %d attempts for subscriber: %s", maxRetries, subName)
						return
					}
				}

				fragmentsPolled := sub.Poll(assembler.OnFragment, 100)
				getIdleStrategy(cfg.IdleStrategy).Idle(fragmentsPolled)
			}
		}
	}()
	return nil
}

func (m *SubscriberManager) newAeronContext() *aeron.Context {
	return aeron.NewContext().AeronDir(m.aeronConfig.Aeron.Directory)
}

func (m *SubscriberManager) createReplaySubscription(
	aeronConn *aeron.Aeron,
	arch *archive.Archive,
	desc *codecs.RecordingDescriptor,
	cfg *config.Subscriber,
	subName string,
) (*aeron.Subscription, error) {
	var subscription *aeron.Subscription
	var position int64
	if m.positionStore(subName) < desc.StartPosition {
		position = desc.StartPosition
	} else {
		position = m.positionStore(subName)
	}
	if cfg.Mode == "replay" {
		replayStreamId := int32(cfg.StreamID + 100)
		replaySessionID, err := arch.StartReplay(desc.RecordingId, position, math.MaxInt64, cfg.Channel, replayStreamId)
		if err != nil {
			return nil, fmt.Errorf("failed to start replay: %w", err)
		}
		log.Debug("%s startReplay replaySessionID=%d", subName, replaySessionID)
		subChannel, err := archive.AddSessionIdToChannel(cfg.Channel, archive.ReplaySessionIdToSessionId(replaySessionID))
		if err != nil {
			return nil, fmt.Errorf("failed to add session id to channel: %w", err)
		}

		log.Debug("Subscribing to channel:%s, stream:%d", subChannel, replayStreamId)
		subscription, err = arch.AddSubscription(subChannel, replayStreamId)
		if err != nil {
			return nil, fmt.Errorf("failed to add subscription: %w", err)
		}
	} else if cfg.Mode == "merge" || cfg.Mode == "merge-with-source-distinction" {
		subChannel := fmt.Sprintf(
			"aeron:udp?control-mode=manual|session-id=%d",
			desc.SessionId,
		)
		log.Debug("Subscribing to channel:%s", subChannel)
		replayChannelUri, _ := aeron.ParseChannelUri(cfg.ReplayChannel)
		replayChannelUri.SetSessionID(desc.SessionId)
		replayChannel := replayChannelUri.String()
		var err error
		subscription, err = aeronConn.AddSubscription(subChannel, int32(cfg.StreamID))
		if err != nil {
			return nil, err
		}
		log.Debug("subscriber: %s replay channel: %s, replayDestination: %s, liveDestination: %s", subName, replayChannel, cfg.ReplayChannel, cfg.Channel)
		replayMerge, err := replaymerge.NewReplayMerge(
			subscription,
			arch,
			replayChannel,
			cfg.ReplayChannel,
			cfg.Channel,
			desc.RecordingId,
			position,
			300000,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to create replay merge: %w", err)
		}
		defer replayMerge.Close()
		handlerName := subName
		if cfg.Mode == "merge-with-source-distinction" {
			handlerName += "-with-source-distinction"
		}
		handler, err := GetHandlerProvider().Get(handlerName)
		for !replayMerge.IsMerged() {
			fragments, err := replayMerge.Poll(handler.OnFragment, 100)
			if err != nil {
				return nil, fmt.Errorf("polling error during replay merge: %w", err)
			}
			strategy := getIdleStrategy(cfg.IdleStrategy)
			strategy.Idle(fragments)
		}
		replayMerge.Close()
		log.Debug("subscriber: %s merge completed", subName)
	}

	return subscription, nil
}

func (m *SubscriberManager) retryLoop() {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()
	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			m.retryFailed()
		}
	}
}

func (m *SubscriberManager) retryFailed() {
	m.mu.Lock()
	defer m.mu.Unlock()
	if len(m.failedSubs) == 0 {
		return
	}
	success := make([]string, 0)
	for subName := range m.failedSubs {
		// Check if subscriber is still enabled before retrying
		if cfg, exists := m.aeronConfig.Aeron.Subscriber[subName]; !exists || !cfg.Enabled {
			// Remove disabled subscribers from failed list
			success = append(success, subName)
			log.Debug("subscriber: %s is disabled, removing from retry list", subName)
			continue
		}

		if err := m.createRunner(subName); err == nil {
			success = append(success, subName)
		} else {
			log.Error("retry failed for subscriber: %s", subName, err)
		}
	}
	for _, sub := range success {
		delete(m.failedSubs, sub)
	}
}

func getIdleStrategy(name string) idlestrategy.Idler {
	return idlestrategy.NewDefaultBackoffIdleStrategy()
}
