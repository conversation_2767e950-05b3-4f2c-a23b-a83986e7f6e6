aeron:
  directory: "/tmp/aeron"
  
  # Metrics configuration
  metrics:
    enabled: true
    port: 8080
  
  # Client configurations
  clients:
    sor-client:
      target-name: "sor-client"
      client-id: 1001
      mode: "cluster"
      channel: "aeron:udp?endpoint=localhost:40123"
      stream-id: 30
      request-channel: "aeron:udp?endpoint=localhost:8010"
      request-stream-id: 10
      response-channel: "aeron:udp?endpoint=localhost:8011"
      response-stream-id: 21
      buffer-size: 262144
    
    match-client:
      target-name: "match-client"
      client-id: 1002
      mode: "archive"
      channel: "aeron:udp?endpoint=localhost:40124"
      stream-id: 31
      request-channel: "aeron:udp?endpoint=localhost:8012"
      request-stream-id: 11
      response-channel: "aeron:udp?endpoint=localhost:8013"
      response-stream-id: 22
      buffer-size: 262144

  # Subscriber configurations
  subscriber:
    sor:
      enabled: true
      mode: merge
      channel: aeron:udp?control=event-ledger:45888|control-mode=dynamic
      stream-id: 40
      request-channel: aeron:udp?endpoint=event-ledger:28010
      response-channel: aeron:udp?endpoint=${POD_IP}:38182
      response-stream-id: 21
      replay-channel: aeron:udp?endpoint=${POD_IP}:38181
    
    match:
      enabled: false
      mode: merge
      channel: aeron:udp?control=event-ledger:45888|control-mode=dynamic
      stream-id: 43
      request-channel: aeron:udp?endpoint=event-ledger:28010
      response-channel: aeron:udp?endpoint=${POD_IP}:38183
      response-stream-id: 21
      replay-channel: aeron:udp?endpoint=${POD_IP}:38184

  # Archive configuration
  archive:
    control-request-channel: "aeron:udp?endpoint=localhost:8010"
    control-response-channel: "aeron:udp?endpoint=localhost:8011"
    directory: "/tmp/aeron-archive"

  # Cluster configuration
  cluster:
    directory: "/tmp/aeron-cluster"

  # Service configuration
  service:
    idle-strategy: "backoff"
